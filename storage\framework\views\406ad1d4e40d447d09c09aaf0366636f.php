<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Sessions - WhatsApp Gateway</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-green-600 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-2xl font-bold">
                    <i class="fab fa-whatsapp mr-2"></i>
                    WhatsApp Gateway
                </h1>
                <div class="space-x-4">
                    <a href="<?php echo e(route('whatsapp.dashboard')); ?>" class="hover:text-green-200">Dashboard</a>
                    <a href="<?php echo e(route('whatsapp.sessions')); ?>" class="hover:text-green-200 font-semibold">Sessions</a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mx-auto p-6">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">Session Management</h2>
                <button onclick="createSession()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    <i class="fas fa-plus mr-2"></i>Create New Session
                </button>
            </div>

            <!-- Sessions Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="sessions-grid">
                <?php $__currentLoopData = $sessions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg shadow-md p-6 session-card" data-session-id="<?php echo e($session['sessionId']); ?>">
                        <!-- Session Header -->
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="font-semibold text-lg"><?php echo e($session['sessionId']); ?></h3>
                                <span class="px-2 py-1 rounded-full text-xs
                                    <?php if($session['status'] === 'connected'): ?> bg-green-100 text-green-800
                                    <?php elseif($session['status'] === 'qr'): ?> bg-yellow-100 text-yellow-800
                                    <?php elseif($session['status'] === 'connecting'): ?> bg-blue-100 text-blue-800
                                    <?php else: ?> bg-red-100 text-red-800
                                    <?php endif; ?>">
                                    <?php echo e(ucfirst($session['status'])); ?>

                                </span>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="refreshSession('<?php echo e($session['sessionId']); ?>')" 
                                        class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-sync"></i>
                                </button>
                                <button onclick="deleteSession('<?php echo e($session['sessionId']); ?>')" 
                                        class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Session Info -->
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Phone:</span>
                                <span class="font-mono"><?php echo e($session['info']['id'] ?? 'N/A'); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Name:</span>
                                <span><?php echo e($session['info']['name'] ?? 'N/A'); ?></span>
                            </div>
                        </div>

                        <!-- QR Code Display -->
                        <?php if($session['status'] === 'qr' && isset($session['qr'])): ?>
                            <div class="mb-4 text-center">
                                <p class="text-sm text-gray-600 mb-2">Scan QR Code with WhatsApp:</p>
                                <img src="<?php echo e($session['qr']); ?>" alt="QR Code" class="mx-auto max-w-full h-48 object-contain">
                            </div>
                        <?php endif; ?>

                        <!-- Action Buttons -->
                        <div class="flex flex-wrap gap-2">
                            <a href="<?php echo e(route('whatsapp.session.detail', $session['sessionId'])); ?>" 
                               class="flex-1 bg-blue-600 text-white text-center py-2 px-3 rounded text-sm hover:bg-blue-700">
                                <i class="fas fa-eye mr-1"></i>Details
                            </a>
                            
                            <?php if($session['status'] === 'connected'): ?>
                                <a href="<?php echo e(route('whatsapp.send.message', $session['sessionId'])); ?>" 
                                   class="flex-1 bg-green-600 text-white text-center py-2 px-3 rounded text-sm hover:bg-green-700">
                                    <i class="fas fa-paper-plane mr-1"></i>Send
                                </a>
                                <a href="<?php echo e(route('whatsapp.messages', $session['sessionId'])); ?>" 
                                   class="flex-1 bg-purple-600 text-white text-center py-2 px-3 rounded text-sm hover:bg-purple-700">
                                    <i class="fas fa-comments mr-1"></i>Messages
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php if(count($sessions) === 0): ?>
                <div class="text-center py-12">
                    <i class="fas fa-mobile-alt text-6xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No Sessions Found</h3>
                    <p class="text-gray-500 mb-6">Create your first WhatsApp session to get started.</p>
                    <button onclick="createSession()" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700">
                        <i class="fas fa-plus mr-2"></i>Create Your First Session
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Create Session Modal -->
    <div id="createSessionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg p-6 w-full max-w-md">
                <h3 class="text-lg font-semibold mb-4">Create New Session</h3>
                <form id="createSessionForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Session ID</label>
                        <input type="text" id="sessionId" class="w-full border rounded px-3 py-2" 
                               placeholder="Enter unique session ID (e.g., device1, main, etc.)" required>
                        <p class="text-xs text-gray-500 mt-1">Use alphanumeric characters and underscores only</p>
                    </div>
                    <div class="flex justify-end space-x-4">
                        <button type="button" onclick="closeCreateSessionModal()" 
                                class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                        <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                            <i class="fas fa-plus mr-2"></i>Create Session
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg p-6 text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Creating session...</p>
            </div>
        </div>
    </div>

    <script>
        // Socket.IO connection
        const socket = io('<?php echo e(config("whatsapp.socket_url", "http://localhost:3000")); ?>');

        socket.on('connect', () => {
            console.log('Connected to WhatsApp Gateway');
        });

        socket.on('session-status', (data) => {
            updateSessionCard(data.sessionId, data.status, data.info);
        });

        socket.on('qr-updated', (data) => {
            updateQRCode(data.sessionId, data.qr);
        });

        socket.on('session-created', (data) => {
            hideLoading();
            if (data.success) {
                location.reload();
            }
        });

        socket.on('session-error', (data) => {
            hideLoading();
            alert('Session error: ' + data.error);
        });

        // Functions
        function createSession() {
            document.getElementById('createSessionModal').classList.remove('hidden');
        }

        function closeCreateSessionModal() {
            document.getElementById('createSessionModal').classList.add('hidden');
            document.getElementById('createSessionForm').reset();
        }

        function showLoading() {
            document.getElementById('loadingModal').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingModal').classList.add('hidden');
        }

        document.getElementById('createSessionForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const sessionId = document.getElementById('sessionId').value;

            // Validate session ID
            if (!/^[a-zA-Z0-9_]+$/.test(sessionId)) {
                alert('Session ID can only contain letters, numbers, and underscores');
                return;
            }

            closeCreateSessionModal();
            showLoading();

            try {
                const response = await fetch('/api/whatsapp/sessions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    body: JSON.stringify({ session_id: sessionId })
                });

                const result = await response.json();

                if (result.success) {
                    // Socket will handle the success response
                    setTimeout(() => {
                        hideLoading();
                        location.reload();
                    }, 3000);
                } else {
                    hideLoading();
                    alert('Failed to create session: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                hideLoading();
                alert('Error creating session: ' + error.message);
            }
        });

        async function deleteSession(sessionId) {
            if (!confirm(`Are you sure you want to delete session "${sessionId}"?`)) return;

            try {
                const response = await fetch(`/api/whatsapp/sessions/${sessionId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    location.reload();
                } else {
                    alert('Failed to delete session: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                alert('Error deleting session: ' + error.message);
            }
        }

        function refreshSession(sessionId) {
            location.reload();
        }

        function updateSessionCard(sessionId, status, info) {
            const card = document.querySelector(`[data-session-id="${sessionId}"]`);
            if (!card) return;

            // Update status badge
            const statusBadge = card.querySelector('span');
            statusBadge.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            statusBadge.className = 'px-2 py-1 rounded-full text-xs ';
            
            if (status === 'connected') {
                statusBadge.className += 'bg-green-100 text-green-800';
            } else if (status === 'qr') {
                statusBadge.className += 'bg-yellow-100 text-yellow-800';
            } else if (status === 'connecting') {
                statusBadge.className += 'bg-blue-100 text-blue-800';
            } else {
                statusBadge.className += 'bg-red-100 text-red-800';
            }

            // Update info if available
            if (info) {
                const phoneSpan = card.querySelector('.font-mono');
                const nameSpan = phoneSpan.parentElement.nextElementSibling.querySelector('span:last-child');
                
                phoneSpan.textContent = info.id || 'N/A';
                nameSpan.textContent = info.name || 'N/A';
            }
        }

        function updateQRCode(sessionId, qr) {
            const card = document.querySelector(`[data-session-id="${sessionId}"]`);
            if (!card) return;

            // Check if QR code section already exists
            let qrSection = card.querySelector('.qr-section');
            
            if (!qrSection) {
                // Create QR section
                qrSection = document.createElement('div');
                qrSection.className = 'qr-section mb-4 text-center';
                qrSection.innerHTML = `
                    <p class="text-sm text-gray-600 mb-2">Scan QR Code with WhatsApp:</p>
                    <img src="${qr}" alt="QR Code" class="mx-auto max-w-full h-48 object-contain">
                `;
                
                // Insert before action buttons
                const actionButtons = card.querySelector('.flex.flex-wrap.gap-2');
                actionButtons.parentNode.insertBefore(qrSection, actionButtons);
            } else {
                // Update existing QR code
                const img = qrSection.querySelector('img');
                img.src = qr;
            }
        }
    </script>
</body>
</html><?php /**PATH C:\laragon\www\larawamd\resources\views/whatsapp/sessions.blade.php ENDPATH**/ ?>