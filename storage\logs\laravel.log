[2025-06-15 23:00:42] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'migration' in 'field list' (Connection: mysql, SQL: select `migration` from `migrations` order by `batch` asc, `migration` asc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'migration' in 'field list' (Connection: mysql, SQL: select `migration` from `migrations` order by `batch` asc, `migration` asc) at C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `migrati...', Array, Object(Closure))
#1 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `migrati...', Array, Object(Closure))
#2 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `migrati...', Array, false)
#3 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3142): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3144): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(53): Illuminate\\Database\\Query\\Builder->pluck('migration')
#7 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(737): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getRan()
#8 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(128): Illuminate\\Database\\Migrations\\Migrator->hasRunAnyMigrations()
#9 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#10 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#11 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#12 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#13 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#18 C:\\laragon\\www\\larawamd\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon\\www\\larawamd\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\laragon\\www\\larawamd\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon\\www\\larawamd\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon\\www\\larawamd\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'migration' in 'field list' at C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `migrati...')
#1 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `migrati...', Array)
#2 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `migrati...', Array, Object(Closure))
#3 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(431): Illuminate\\Database\\Connection->run('select `migrati...', Array, Object(Closure))
#4 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2914): Illuminate\\Database\\Connection->select('select `migrati...', Array, false)
#5 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3142): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3144): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(53): Illuminate\\Database\\Query\\Builder->pluck('migration')
#9 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(737): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getRan()
#10 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(128): Illuminate\\Database\\Migrations\\Migrator->hasRunAnyMigrations()
#11 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#13 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#14 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#15 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#20 C:\\laragon\\www\\larawamd\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 C:\\laragon\\www\\larawamd\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon\\www\\larawamd\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon\\www\\larawamd\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon\\www\\larawamd\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon\\www\\larawamd\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 {main}
"} 
[2025-06-15 23:01:26] testing.ERROR: Failed to fetch sessions for dashboard {"error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 04:30:57] local.ERROR: Failed to fetch sessions for dashboard {"error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:36:30] local.ERROR: Failed to fetch sessions for dashboard {"error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:36:41] local.ERROR: WhatsApp session creation failed {"session_id":"11","error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:36:43] local.ERROR: WhatsApp session creation failed {"session_id":"11","error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:36:53] local.ERROR: Failed to fetch sessions {"error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:36:55] local.ERROR: Failed to fetch sessions {"error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:37:40] local.ERROR: Failed to fetch sessions for dashboard {"error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:37:48] local.ERROR: Failed to fetch sessions for dashboard {"error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:38:26] local.ERROR: Failed to fetch WhatsApp sessions {"error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:38:28] local.ERROR: WhatsApp session creation failed {"session_id":"test-session-1750059506256","error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:38:31] local.ERROR: Failed to fetch WhatsApp sessions {"error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:52:02] local.ERROR: Failed to fetch WhatsApp sessions {"error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:52:05] local.ERROR: WhatsApp session creation failed {"session_id":"test-session-1750060322528","error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
[2025-06-16 07:52:07] local.ERROR: Failed to fetch WhatsApp sessions {"error":"cURL error 7: Failed to connect to localhost port 3000: Connection refused (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:3000/api/sessions"} 
